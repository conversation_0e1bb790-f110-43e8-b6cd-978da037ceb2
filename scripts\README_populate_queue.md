# Email Queue Population Script

This script populates the `email_queue` table with initial cold emails, distributing them across multiple sender accounts with daily limits and round-robin scheduling.

## Features

- ✅ **Account Validation**: Validates sender accounts against the database
- ✅ **Round-Robin Distribution**: Distributes emails evenly across sender accounts
- ✅ **Daily Limits**: Respects daily sending limits per account
- ✅ **Template Selection**: Randomly selects from available email templates
- ✅ **Dry Run Mode**: Preview scheduling without writing to database
- ✅ **Comprehensive Logging**: Detailed logging and statistics
- ✅ **Error Handling**: Graceful error handling and reporting

## Configuration

Edit the configuration parameters at the top of `populate_email_queue.py`:

```python
# List of sender email addresses (must exist in email_accounts table)
accounts = [
    "<EMAIL>",
    # Add more sender accounts here
]

# Daily limits per account [day1, day2, day3, day4+]
daily_limits = [3, 5, 7, 10]

# Whether to use production emails (True) or warm-up emails (False)
production_mails = True

# Path to the JSON file containing recipient emails
emails_json_file = "emails.json"

# Enable dry-run mode to preview scheduling without writing to DB
dry_run = True

# Starting date for scheduling (None = today)
start_date = None
```

## Input Files

### emails.json

Create a JSON file with recipient emails:

```json
[
  { "email": "<EMAIL>" },
  { "email": "<EMAIL>" },
  { "email": "<EMAIL>" }
]
```

## Usage

### 1. Dry Run (Preview)

```bash
cd /path/to/backend
python scripts/populate_email_queue.py
```

This will:
- Validate all configurations
- Show what emails would be scheduled
- Save results to `dry_run_results.json`
- **Not write anything to the database**

### 2. Actual Scheduling

1. Set `dry_run = False` in the script
2. Run the script:

```bash
python scripts/populate_email_queue.py
```

## How It Works

### 1. Account Validation
- Checks that all sender accounts exist in `email_accounts` table
- Verifies accounts are active

### 2. Round-Robin Distribution
- Distributes emails evenly across sender accounts
- Uses modulo operator for fair distribution

### 3. Daily Limits
- Day 1: Max 3 emails per account
- Day 2: Max 5 emails per account
- Day 3: Max 7 emails per account
- Day 4+: Max 10 emails per account (default)

### 4. Scheduling Logic
- Starts from specified date (or today)
- For each email:
  - Selects next account (round-robin)
  - Finds first available day within limits
  - Schedules between 9 AM - 5 PM with random distribution
  - Moves to next account

### 5. Template Selection
- Selects from `email_content` table
- Filters by `is_warm_up` and `follow_up = 0`
- Uses round-robin selection

## Output

### Console Output
```
==================================================
STEP 1: Validating sender accounts
==================================================
✓ Found account: <EMAIL> (ID: abc123...)

==================================================
STEP 2: Loading recipient emails
==================================================
Loaded 20 recipient emails from emails.json

==================================================
EMAIL SCHEDULING SUMMARY
==================================================
Total recipients: 20
Total accounts: 1
Emails scheduled: 20
Errors: 0

Emails per account:
  <EMAIL>: 20 emails

Emails per day:
  mateusz@paneljazdy.pl_2025-05-25: 3 emails
  mateusz@paneljazdy.pl_2025-05-26: 5 emails
  mateusz@paneljazdy.pl_2025-05-27: 7 emails
  mateusz@paneljazdy.pl_2025-05-28: 5 emails
```

### Dry Run Results File
The script generates `dry_run_results.json` with **exact database records** that would be inserted:

```json
{
  "summary": {
    "total_recipients": 20,
    "total_accounts": 1,
    "emails_scheduled": 20,
    "emails_per_account": {
      "<EMAIL>": 20
    },
    "emails_per_day": {
      "mateusz@paneljazdy.pl_2025-05-25": 3,
      "mateusz@paneljazdy.pl_2025-05-26": 5
    },
    "errors": [],
    "generated_at": "2025-05-25T21:31:58"
  },
  "database_records": {
    "email_queue_inserts": [
      {
        "account_id": "cee71841-b9bb-4643-bd09-c153c08bf681",
        "recipient_email": "<EMAIL>",
        "content_id": "********-0000-0000-0000-********0004",
        "scheduled_time": "2025-05-25T11:42:00+00:00",
        "status": "scheduled",
        "follow_up": 0,
        "is_follow_up": false,
        "_meta": {
          "recipient_email": "<EMAIL>",
          "sender_email": "<EMAIL>",
          "template_subject": "Re: Initial Contact - Partnership Opportunity (Follow-up 3)",
          "template_body_preview": "Hello, I hope you're doing well...",
          "day_number": 1,
          "daily_limit": 3,
          "scheduled_date": "2025-05-25",
          "scheduled_time_local": "2025-05-25 11:42:00 UTC"
        }
      }
    ],
    "total_email_queue_records": 20,
    "description": "These are the exact records that would be inserted into the email_queue table"
  }
}
```

## Database Tables Used

- **email_accounts**: Sender account validation
- **email_content**: Email template selection
- **email_queue**: Email scheduling

## Error Handling

The script handles various error scenarios:
- Invalid sender accounts
- Missing email templates
- Database connection issues
- Invalid recipient emails
- Scheduling conflicts

All errors are logged and included in the summary report.
