#!/usr/bin/env python3
"""
Main server module for the Cold Email Server
"""
import socketserver
import signal
import sys

from app.config import SERVER_CONFIG, EMAIL_CONFIG
from app.utils.logger import setup_logger
from app.utils.keep_alive import start_ping_pong_cycle, stop_ping_pong_cycle
from app.tracking.handlers import test_tracking_pixel
from app.server.handlers import EmailHandler
from app.server.queue import start_email_processor, stop_email_processor, setup_test_email
from app.scheduling.scheduler_service import scheduler_service

# Set up logger
logger = setup_logger('server.main')

# Global variables for cleanup
httpd = None
keep_alive_enabled = False
process_emails_enabled = False
use_scheduler = False

def signal_handler(signum, _):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received signal {signum}, shutting down gracefully...")
    cleanup_and_exit()

def cleanup_and_exit():
    """Clean up all resources and exit."""
    global httpd, keep_alive_enabled, process_emails_enabled, use_scheduler

    try:
        # Stop email processing
        if process_emails_enabled:
            if use_scheduler:
                logger.info("Stopping scheduler service...")
                scheduler_service.stop()
            else:
                logger.info("Stopping email processor...")
                stop_email_processor()

        # Stop keep-alive
        if keep_alive_enabled:
            logger.info("Stopping keep-alive mechanism...")
            stop_ping_pong_cycle()

        # Close the server
        if httpd:
            logger.info("Closing HTTP server...")
            httpd.server_close()

        logger.info("Server shutdown complete")

    except Exception as e:
        logger.error(f"Error during cleanup: {str(e)}")

    sys.exit(0)

def run_server(host=None, port=None, keep_alive_arg=True, process_emails_enabled_arg=True, use_scheduler_arg=False):
    """
    Run the HTTP server.

    Args:
        host (str, optional): Host to bind to
        port (int, optional): Port to bind to
        keep_alive_arg (bool, optional): Whether to start the keep-alive mechanism
        process_emails_enabled_arg (bool, optional): Whether to process scheduled emails
        use_scheduler_arg (bool, optional): Whether to use the new scheduler service instead of the old queue system
    """
    global httpd, keep_alive_enabled, process_emails_enabled, use_scheduler

    # Set global variables for cleanup
    keep_alive_enabled = keep_alive_arg
    process_emails_enabled = process_emails_enabled_arg
    use_scheduler = use_scheduler_arg

    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    # Use configuration values if not provided
    if host is None:
        host = SERVER_CONFIG['host']
    if port is None:
        port = SERVER_CONFIG['port']

    # Print some helpful information
    logger.info(f"Server starting with configuration:")
    logger.info(f"  - Host: {host}")
    logger.info(f"  - Port: {port}")
    logger.info(f"  - Base URL: {SERVER_CONFIG['base_url']}")
    logger.info(f"  - Keep-alive: {'enabled' if keep_alive_enabled else 'disabled'}")
    logger.info(f"  - Email processing: {'enabled' if process_emails_enabled else 'disabled'}")
    logger.info(f"  - Email system: {'new scheduler service' if use_scheduler else 'legacy queue system'}")
    logger.info(f"  - Follow-up emails: {'enabled' if EMAIL_CONFIG['send_follow_ups'] else 'disabled'}")
    logger.info(f"  - Test mode: {'enabled' if SERVER_CONFIG['test_mode'] else 'disabled'}")

    # Create test tracking pixels
    test_tracking_pixel()

    # Set up test email if in test mode
    if SERVER_CONFIG['test_mode']:
        setup_test_email()

    # Start the server
    httpd = socketserver.TCPServer((host, port), EmailHandler)
    logger.info(f"Server started at {SERVER_CONFIG['base_url']}")
    logger.info(f"Tracking statistics available at {SERVER_CONFIG['base_url']}/tracking")

    # Start the ping-pong cycle to keep the server awake on Render
    if keep_alive_enabled:
        logger.info("Starting keep-alive mechanism to prevent server sleep")
        start_ping_pong_cycle()

    # Start the email processing system
    if process_emails_enabled:
        if use_scheduler:
            logger.info("Starting production scheduler service")
            logger.info("  - Daily preprocessing at 5:00 AM")
            logger.info("  - Email sending during business hours (9 AM - 5 PM)")
            logger.info("  - Quota management and postponement rules")
            scheduler_service.start()
        else:
            logger.info("Starting legacy scheduled email processor")

            # In test mode, we still pass the normal interval, but the processor will use 20 seconds internally
            if SERVER_CONFIG['test_mode']:
                logger.info("Test mode: Database fetch interval will be 20 seconds instead of 2 minutes")

            # Fetch every 2 minutes (or 20 seconds in test mode), but look ahead 60 minutes
            start_email_processor(interval_minutes=2, look_ahead_minutes=60)

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
        cleanup_and_exit()
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        cleanup_and_exit()
