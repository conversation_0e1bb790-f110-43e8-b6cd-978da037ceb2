#!/usr/bin/env python3
"""
<PERSON>ript to set up the warm-up email system:
1. Run the database migration to add is_warm_up and follow_up columns
2. Insert warm-up emails from warm-up-mails.json
"""
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.append(str(Path(__file__).parent.parent))

from app.database.supabase_client import db
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('setup_warm_up_system')

def run_migration():
    """Run the database migration to add new columns."""
    logger.info("Running database migration...")

    try:
        # Check if columns already exist
        response = db.client.table('email_content').select('*').limit(1).execute()
        if response.data and len(response.data) > 0:
            existing_columns = response.data[0].keys()
            logger.info(f"Current email_content columns: {list(existing_columns)}")

            if 'is_warm_up' in existing_columns and 'follow_up' in existing_columns:
                logger.info("Migration columns already exist, skipping migration")
                return True

        # Since we can't run DDL through the REST API, we need to inform the user
        # to run the migration manually in the Supabase SQL editor
        logger.error("Migration needs to be run manually in Supabase SQL editor.")
        logger.error("Please copy and paste the following SQL into your Supabase SQL editor:")
        logger.error("=" * 80)

        migration_file = Path(__file__).parent.parent / 'migrations' / '001_add_warm_up_and_follow_up_to_email_content.sql'
        if migration_file.exists():
            with open(migration_file, 'r', encoding='utf-8') as f:
                migration_sql = f.read()
            logger.error(migration_sql)
        else:
            logger.error("""
-- Add is_warm_up column to email_content table
ALTER TABLE email_content
ADD COLUMN IF NOT EXISTS is_warm_up BOOLEAN NOT NULL DEFAULT FALSE;

-- Add follow_up column to email_content table
ALTER TABLE email_content
ADD COLUMN IF NOT EXISTS follow_up INTEGER NOT NULL DEFAULT 0;

-- Add comments to document the new columns
COMMENT ON COLUMN email_content.is_warm_up IS 'Indicates whether the email is for warm-up (true) or production use (false)';
COMMENT ON COLUMN email_content.follow_up IS 'Indicates which follow-up this email is: 0 = initial message, 1 = 1st follow-up, 2 = 2nd follow-up, etc.';

-- Create index for efficient filtering by warm-up mode and follow-up number
CREATE INDEX IF NOT EXISTS idx_email_content_warm_up_follow_up
ON email_content(is_warm_up, follow_up);

-- Create index for efficient filtering by warm-up mode only
CREATE INDEX IF NOT EXISTS idx_email_content_warm_up
ON email_content(is_warm_up);
            """)

        logger.error("=" * 80)
        logger.error("After running the SQL, please run this script again.")
        return False

    except Exception as e:
        logger.error(f"Error checking migration status: {str(e)}")
        return False

def run_warm_up_insertion():
    """Run the warm-up emails insertion script."""
    logger.info("Running warm-up emails insertion...")

    try:
        # Import and run the insertion script
        from insert_warm_up_emails import insert_warm_up_emails
        success = insert_warm_up_emails()

        if success:
            logger.info("Warm-up emails insertion completed successfully")
            return True
        else:
            logger.error("Warm-up emails insertion failed")
            return False

    except Exception as e:
        logger.error(f"Error running warm-up emails insertion: {str(e)}")
        return False

def main():
    """Main function."""
    logger.info("Starting warm-up email system setup...")

    try:
        # Step 1: Run migration
        migration_success = run_migration()
        if not migration_success:
            logger.error("Migration failed. Aborting setup.")
            return 1

        # Step 2: Insert warm-up emails
        insertion_success = run_warm_up_insertion()
        if not insertion_success:
            logger.error("Warm-up emails insertion failed. Setup incomplete.")
            return 1

        logger.info("Warm-up email system setup completed successfully!")
        logger.info("You can now use the --warm-up flag to enable warm-up mode.")
        return 0

    except Exception as e:
        logger.error(f"Setup failed with error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
