#!/usr/bin/env python3
"""
Script to insert warm-up emails from warm-up-mails.json into the email_content table
"""
import json
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.append(str(Path(__file__).parent.parent))

from app.database.supabase_client import db
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('insert_warm_up_emails')

def load_warm_up_emails():
    """Load warm-up emails from JSON file."""
    json_file_path = Path(__file__).parent.parent / 'warm-up-mails.json'
    
    if not json_file_path.exists():
        logger.error(f"Warm-up emails file not found: {json_file_path}")
        return None
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            emails = json.load(f)
        logger.info(f"Loaded {len(emails)} warm-up emails from {json_file_path}")
        return emails
    except Exception as e:
        logger.error(f"Error loading warm-up emails: {str(e)}")
        return None

def insert_warm_up_emails():
    """Insert warm-up emails into the email_content table."""
    # Load emails from JSON
    emails = load_warm_up_emails()
    if not emails:
        return False
    
    logger.info("Starting to insert warm-up emails...")
    
    inserted_count = 0
    for i, email in enumerate(emails):
        try:
            # Prepare email content data
            content_data = {
                'subject': email['subject'],
                'body': email['body'],
                'is_warm_up': True,
                'follow_up': 0  # All warm-up emails are initial emails (follow_up = 0)
            }
            
            # Check if this email already exists
            response = db.client.table('email_content').select('id').eq('subject', email['subject']).eq('body', email['body']).eq('is_warm_up', True).execute()
            existing = db._handle_response(response)
            
            if existing and len(existing) > 0:
                logger.info(f"Email {i+1} already exists, skipping: {email['subject'][:50]}...")
                continue
            
            # Insert the email
            response = db.client.table('email_content').insert(content_data).execute()
            result = db._handle_response(response)
            
            if result and len(result) > 0:
                inserted_count += 1
                logger.info(f"Inserted email {i+1}/{len(emails)}: {email['subject'][:50]}...")
            else:
                logger.error(f"Failed to insert email {i+1}: {email['subject'][:50]}...")
                
        except Exception as e:
            logger.error(f"Error inserting email {i+1}: {str(e)}")
            continue
    
    logger.info(f"Successfully inserted {inserted_count} warm-up emails")
    return inserted_count > 0

def main():
    """Main function."""
    logger.info("Starting warm-up emails insertion script...")
    
    try:
        success = insert_warm_up_emails()
        if success:
            logger.info("Warm-up emails insertion completed successfully!")
            return 0
        else:
            logger.error("Warm-up emails insertion failed!")
            return 1
    except Exception as e:
        logger.error(f"Script failed with error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
