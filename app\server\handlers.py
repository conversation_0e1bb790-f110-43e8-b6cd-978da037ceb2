#!/usr/bin/env python3
"""
HTTP request handlers for the Cold Email Server
"""
import json
import http.server
import datetime

from app.utils.logger import setup_logger
from app.utils.keep_alive import handle_ping, handle_pong
from app.email.sender import send_email
from app.tracking.handlers import handle_tracking_pixel, handle_tracking_stats
from app.database.supabase_client import db
from app.server.endpoints import (
    get_priority_queue_state,
    get_all_emails_in_queue,
    process_scheduled_emails_test,
    get_scheduler_status,
    force_daily_preprocessing
)

# Set up logger
logger = setup_logger('server.handlers')

class EmailHandler(http.server.BaseHTTPRequestHandler):
    """HTTP request handler for email sending and tracking endpoints."""

    def _set_response(self, status_code=200, content_type='application/json'):
        """Set the response headers."""
        self.send_response(status_code)
        self.send_header('Content-Type', content_type)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

    def do_GET(self):
        """Handle GET requests."""
        # Handle tracking pixel requests
        if self.path.startswith('/track/'):
            status_code, content_type, content = handle_tracking_pixel(
                self.path,
                self.headers,
                self.client_address
            )

            self.send_response(status_code)
            self.send_header('Content-Type', content_type)
            self.send_header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
            self.end_headers()
            self.wfile.write(content)
            return

        # Handle tracking statistics endpoint
        elif self.path.startswith('/tracking'):
            status_code, content_type, content = handle_tracking_stats(self.path)

            self.send_response(status_code)
            self.send_header('Content-Type', content_type)
            self.end_headers()
            self.wfile.write(content)
            return

        # Handle email tracking data endpoint
        elif self.path == '/email-tracking':
            self._set_response(200)
            try:
                # Get all tracking data from Supabase
                tracking_data = db.get_email_tracking_data()

                # Process each tracking record to add email and recipient information
                enhanced_tracking = []
                for record in tracking_data:
                    email_id = record.get('email_id')
                    email = db.get_email_by_id(email_id)

                    if email:
                        recipient_email = email.get('recipient_email', 'Unknown')

                        # Format timestamps for better readability
                        open_time = record.get('open_time')
                        if open_time:
                            try:
                                dt = datetime.datetime.fromisoformat(open_time.replace('Z', '+00:00'))
                                open_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                            except (ValueError, AttributeError):
                                pass

                        enhanced_record = {
                            "id": record.get('id'),
                            "email_id": email_id,
                            "tracking_id": record.get('tracking_id'),
                            "recipient_email": recipient_email,
                            "subject": email.get('subject'),
                            "open_time": open_time,
                            "user_agent": record.get('user_agent'),
                            "ip_address": record.get('ip_address'),
                            "device_type": record.get('device_type'),
                            "browser": record.get('browser'),
                            "os": record.get('os')
                        }

                        enhanced_tracking.append(enhanced_record)

                response = {
                    "status": "success",
                    "message": f"Retrieved {len(enhanced_tracking)} email tracking records",
                    "tracking_data": enhanced_tracking
                }

            except Exception as e:
                logger.error(f"Error retrieving email tracking data: {str(e)}")
                response = {"status": "error", "error": str(e)}

            self.wfile.write(json.dumps(response).encode('utf-8'))
            return

        # Handle ping request (for keep-alive)
        elif self.path == '/ping':
            try:
                self._set_response(200)
                response = handle_ping()
                self.wfile.write(json.dumps(response).encode('utf-8'))
            except ConnectionError as e:
                logger.warning(f"Connection error in ping handler: {str(e)}")
            except Exception as e:
                logger.error(f"Error in ping handler: {str(e)}")
            return

        # Handle pong request (for keep-alive)
        elif self.path == '/pong':
            try:
                self._set_response(200)
                response = handle_pong()
                self.wfile.write(json.dumps(response).encode('utf-8'))
            except ConnectionError as e:
                logger.warning(f"Connection error in pong handler: {str(e)}")
            except Exception as e:
                logger.error(f"Error in pong handler: {str(e)}")
            return

        # Handle priority queue endpoint
        elif self.path == '/priority-queue':
            self._set_response(200)
            try:
                result = get_priority_queue_state()
                response = {
                    "status": "success",
                    "message": "Priority queue state retrieved",
                    "result": result
                }
            except Exception as e:
                logger.error(f"Error retrieving priority queue state: {str(e)}")
                response = {"status": "error", "error": str(e)}
            self.wfile.write(json.dumps(response).encode('utf-8'))
            return

        # Handle all scheduled emails endpoint (for debugging)
        elif self.path == '/all-scheduled-emails':
            self._set_response(200)
            try:
                # Get all scheduled emails from the database
                response_db = db.client.table('email_queue').select('*').eq('status', 'scheduled').execute()
                emails = db._handle_response(response_db)

                # Format the response
                result = {
                    "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "total_emails": len(emails) if emails else 0,
                    "emails": []
                }

                # Process each email
                if emails:
                    for email in emails:
                        # Format timestamps for better readability
                        for key in ['scheduled_time', 'sent_time', 'created_at', 'updated_at', 'last_attempt_time']:
                            if email.get(key):
                                try:
                                    dt = datetime.datetime.fromisoformat(email[key].replace('Z', '+00:00'))
                                    email[key] = dt.strftime('%Y-%m-%d %H:%M:%S')
                                except (ValueError, AttributeError):
                                    pass

                        # Get recipient email
                        recipient_email = email.get('recipient_email', 'Unknown')

                        # Add recipient email to the email data
                        email_data = dict(email)
                        email_data['recipient_email'] = recipient_email

                        result["emails"].append(email_data)

                response = {
                    "status": "success",
                    "message": f"Retrieved {len(result['emails'])} scheduled emails",
                    "result": result
                }
            except Exception as e:
                logger.error(f"Error retrieving all scheduled emails: {str(e)}")
                response = {"status": "error", "error": str(e)}
            self.wfile.write(json.dumps(response).encode('utf-8'))
            return

        # Handle update SQL function endpoint
        elif self.path == '/update-sql-function':
            self._set_response(200)
            try:
                # SQL to update the get_emails_to_send function
                sql = """
                -- Drop the old get_emails_to_send function to allow changing parameter defaults
                DROP FUNCTION IF EXISTS get_emails_to_send(integer);

                CREATE OR REPLACE FUNCTION get_emails_to_send(
                  time_window_minutes INTEGER DEFAULT 5
                )
                RETURNS TABLE (
                  id UUID,
                  account_id UUID,
                  recipient_email VARCHAR(255),
                  subject VARCHAR(255),
                  body TEXT,
                  tracking_id UUID,
                  scheduled_time TIMESTAMP WITH TIME ZONE,
                  sender_email VARCHAR(255),
                  smtp_server VARCHAR(255),
                  smtp_port INTEGER,
                  use_ssl BOOLEAN,
                  is_follow_up BOOLEAN,
                  follow_up INTEGER,
                  previous_email_id UUID
                ) AS $$
                BEGIN
                  RETURN QUERY
                  SELECT
                    eq.id,
                    eq.account_id,
                    eq.recipient_email,
                    ec.subject,
                    ec.body,
                    eq.tracking_id,
                    eq.scheduled_time,
                    ea.email AS sender_email,
                    ea.smtp_server,
                    ea.smtp_port,
                    ea.use_ssl,
                    eq.is_follow_up,
                    eq.follow_up,
                    eq.previous_email_id
                  FROM email_queue eq
                  JOIN email_accounts ea ON eq.account_id = ea.id
                  JOIN email_content ec ON eq.content_id = ec.id
                  WHERE eq.status = 'scheduled'
                  -- Use explicit timezone conversion to ensure consistent behavior
                  AND eq.scheduled_time <= (NOW() AT TIME ZONE 'UTC' + (time_window_minutes || ' minutes')::INTERVAL)
                  AND ea.active = TRUE
                  ORDER BY eq.scheduled_time;
                END;
                $$ LANGUAGE plpgsql;
                """

                # Execute the SQL
                response_sql = db.client.rpc('exec_sql', {'sql': sql}).execute()

                response = {
                    "status": "success",
                    "message": "SQL function updated successfully",
                    "result": response_sql.data if hasattr(response_sql, 'data') else None
                }
            except Exception as e:
                logger.error(f"Error updating SQL function: {str(e)}")
                response = {"status": "error", "error": str(e)}
            self.wfile.write(json.dumps(response).encode('utf-8'))
            return

        # Handle email queue inspection endpoint for testing
        elif self.path == '/email-queue-status':
            self._set_response(200)
            try:
                result = process_scheduled_emails_test()
                response = {
                    "status": "success",
                    "message": "Email queue status retrieved",
                    "result": result
                }
            except Exception as e:
                logger.error(f"Error retrieving email queue status: {str(e)}")
                response = {"status": "error", "error": str(e)}
            self.wfile.write(json.dumps(response).encode('utf-8'))
            return

        # Handle email queue retrieval endpoint
        elif self.path == '/email-queue':
            self._set_response(200)
            try:
                # Get all emails from the queue
                result = get_all_emails_in_queue()
                response = {
                    "status": "success",
                    "message": f"Retrieved {len(result['emails'])} emails from the queue",
                    "result": result
                }
            except Exception as e:
                logger.error(f"Error retrieving email queue: {str(e)}")
                response = {"status": "error", "error": str(e)}

            self.wfile.write(json.dumps(response).encode('utf-8'))
            return

        # Handle scheduler status endpoint
        elif self.path == '/scheduler-status':
            self._set_response(200)
            try:
                result = get_scheduler_status()
                response = {
                    "status": "success",
                    "message": "Scheduler status retrieved",
                    "result": result
                }
            except Exception as e:
                logger.error(f"Error retrieving scheduler status: {str(e)}")
                response = {"status": "error", "error": str(e)}
            self.wfile.write(json.dumps(response).encode('utf-8'))
            return

        # Handle force preprocessing endpoint
        elif self.path == '/force-preprocessing':
            self._set_response(200)
            try:
                result = force_daily_preprocessing()
                response = {
                    "status": "success",
                    "message": "Daily preprocessing completed",
                    "result": result
                }
            except Exception as e:
                logger.error(f"Error running daily preprocessing: {str(e)}")
                response = {"status": "error", "error": str(e)}
            self.wfile.write(json.dumps(response).encode('utf-8'))
            return

        # Handle server status endpoint
        elif self.path == '/':
            self._set_response(200, 'text/plain')
            self.wfile.write("Cold Email Server is running!".encode('utf-8'))
            return

        # Handle unknown endpoints
        else:
            self._set_response(404)
            response = {"error": "Endpoint not found"}
            self.wfile.write(json.dumps(response).encode('utf-8'))

    def do_POST(self):
        """Handle POST requests."""
        try:
            # Get content length
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)

            # Handle email sending endpoint
            if self.path == '/send-email':
                try:
                    # Parse JSON data
                    data = json.loads(post_data.decode('utf-8'))

                    # Check required fields
                    if not all(k in data for k in ("recipient", "subject", "body")):
                        self._set_response(400)
                        response = {"error": "Missing required fields (recipient, subject, body)"}
                        self.wfile.write(json.dumps(response).encode('utf-8'))
                        return

                    # Extract email_id if present (for updating existing emails)
                    email_id = data.get("email_id")

                    # Send email
                    success, tracking_id = send_email(
                        data["recipient"],
                        data["subject"],
                        data["body"],
                        email_id=email_id
                    )

                    if success:
                        self._set_response(200)
                        response = {
                            "status": "success",
                            "tracking_id": tracking_id,
                            "tracking_enabled": True
                        }
                    else:
                        self._set_response(500)
                        response = {"status": "error", "error": tracking_id}

                    self.wfile.write(json.dumps(response).encode('utf-8'))

                except json.JSONDecodeError:
                    self._set_response(400)
                    response = {"error": "Invalid JSON data"}
                    self.wfile.write(json.dumps(response).encode('utf-8'))

                except Exception as e:
                    logger.error(f"Server error in /send-email: {str(e)}")
                    self._set_response(500)
                    response = {"error": f"Server error: {str(e)}"}
                    self.wfile.write(json.dumps(response).encode('utf-8'))

            else:
                self._set_response(404)
                response = {"error": "Endpoint not found"}
                self.wfile.write(json.dumps(response).encode('utf-8'))

        except Exception as e:
            logger.error(f"Server error in do_POST: {str(e)}")
            self._set_response(500)
            response = {"error": f"Server error: {str(e)}"}
            self.wfile.write(json.dumps(response).encode('utf-8'))