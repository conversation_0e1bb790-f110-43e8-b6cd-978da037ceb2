# Project Structure

This document describes the clean, organized structure of the Cold Email Server project.

## Directory Structure

```
backend/
├── app/                          # Main application package
│   ├── __init__.py
│   ├── config.py                 # Configuration settings
│   ├── database/                 # Database layer
│   │   ├── __init__.py
│   │   └── supabase_client.py    # Supabase database client
│   ├── email/                    # Email functionality
│   │   ├── __init__.py
│   │   └── sender.py             # Email sending logic
│   ├── scheduling/               # Production email scheduling
│   │   ├── __init__.py
│   │   ├── daily_preprocessor.py # Daily email preprocessing
│   │   ├── quota_manager.py      # Quota management system
│   │   └── scheduler_service.py  # Main scheduler service
│   ├── server/                   # HTTP server components
│   │   ├── __init__.py
│   │   ├── endpoints.py          # API endpoints
│   │   ├── handlers.py           # HTTP request handlers
│   │   ├── main.py               # Server startup logic
│   │   └── queue.py              # Legacy queue system (for test mode)
│   ├── tracking/                 # Email tracking functionality
│   │   ├── __init__.py
│   │   ├── database.py           # Tracking database operations
│   │   ├── handlers.py           # Tracking request handlers
│   │   └── pixels.py             # Tracking pixel generation
│   └── utils/                    # Utility modules
│       ├── __init__.py
│       ├── keep_alive.py         # Server keep-alive mechanism
│       └── logger.py             # Logging configuration
├── main.py                       # Application entry point
├── requirements.txt              # Python dependencies
├── README.md                     # Project documentation
├── PRODUCTION_SCHEDULER_GUIDE.md # Scheduler system guide
└── venv/                         # Virtual environment (not in git)
```

## Core Components

### 1. Main Entry Point
- **`main.py`**: Application entry point with command-line argument parsing
  - Supports both legacy queue system and new scheduler service
  - Use `--use-scheduler` flag for production scheduler

### 2. Configuration
- **`app/config.py`**: Centralized configuration management
  - Server settings, email settings, scheduling configuration
  - Environment variable handling

### 3. Database Layer
- **`app/database/supabase_client.py`**: Supabase database client
  - Email queue operations, account management
  - Email content and template handling

### 4. Email System
- **`app/email/sender.py`**: Email sending functionality
  - SMTP configuration, authentication
  - Email composition and delivery

### 5. Scheduling System (Production)
- **`app/scheduling/scheduler_service.py`**: Main scheduler service
  - Daily preprocessing at 5:00 AM
  - Business hours enforcement (9 AM - 5 PM)
  - Quota management and postponement
- **`app/scheduling/daily_preprocessor.py`**: Daily email preprocessing
  - Fetches and organizes emails for the day
  - Builds priority queues per account
- **`app/scheduling/quota_manager.py`**: Quota management
  - Daily limits (70% follow-ups, 30% initial emails)
  - Postponement logic for excess emails

### 6. Server Components
- **`app/server/main.py`**: HTTP server startup and management
- **`app/server/handlers.py`**: HTTP request routing and handling
- **`app/server/endpoints.py`**: API endpoint implementations
- **`app/server/queue.py`**: Legacy queue system (for test mode)

### 7. Tracking System
- **`app/tracking/`**: Email open tracking and analytics
  - Pixel generation, tracking database, statistics

### 8. Utilities
- **`app/utils/logger.py`**: Centralized logging configuration
- **`app/utils/keep_alive.py`**: Server keep-alive for cloud deployment

## Usage

### Production Mode (Recommended)
```bash
python main.py your_password --use-scheduler
```
- Uses the new scheduler service
- Daily preprocessing at 5:00 AM
- Business hours enforcement
- Quota management

### Test Mode
```bash
python main.py your_password --test-mode
```
- Uses legacy queue system
- 1-minute follow-up intervals
- 20-second database polling

### Development Mode
```bash
python main.py your_password
```
- Uses legacy queue system
- 2-minute database polling
- Full functionality

## Key Features

1. **Dual Email Systems**: Legacy queue system for testing, production scheduler for efficiency
2. **Business Hours**: Emails only sent 9 AM - 5 PM
3. **Quota Management**: 70% follow-ups, 30% initial emails per account daily
4. **Email Tracking**: Open tracking with pixel-based analytics
5. **Multi-Account Support**: Multiple email accounts with individual quotas
6. **Follow-up Automation**: Automatic follow-up scheduling (2, 4, 7, 14, 30 days)

## Maintenance

- All one-time migration scripts have been removed
- Test files have been cleaned up
- Cache directories are excluded from version control
- Clean, focused codebase with minimal dependencies
