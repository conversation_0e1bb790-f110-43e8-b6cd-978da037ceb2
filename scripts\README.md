# Scripts Directory

This directory contains utility scripts for managing the email system.

## Warm-Up Email System Setup

### setup_warm_up_system.py
Main setup script that:
1. Runs the database migration to add `is_warm_up` and `follow_up` columns to the `email_content` table
2. Inserts warm-up emails from `warm-up-mails.json`

**Usage:**
```bash
python scripts/setup_warm_up_system.py
```

### insert_warm_up_emails.py
Standalone script to insert warm-up emails from the JSON file into the database.

**Usage:**
```bash
python scripts/insert_warm_up_emails.py
```

## Database Migration

### migrations/001_add_warm_up_and_follow_up_to_email_content.sql
SQL migration file that adds:
- `is_warm_up` boolean column (default: false)
- `follow_up` integer column (default: 0)
- Appropriate indexes for efficient querying

## How to Use Warm-Up Mode

After running the setup script, you can use warm-up mode by adding the `--warm-up` flag when starting the server:

```bash
python main.py YOUR_APP_PASSWORD --warm-up
```

### Behavior:
- **With `--warm-up` flag**: System randomly selects from warm-up emails (`is_warm_up = true`)
- **Without flag (default)**: System uses production emails (`is_warm_up = false`)
- **Follow-up matching**: System matches the `follow_up` value from `email_queue` with templates in `email_content`

### Email Selection Logic:
1. System checks if warm-up mode is enabled
2. Queries `email_content` table filtering by:
   - `is_warm_up` = current mode (true/false)
   - `follow_up` = value from `email_queue.follow_up`
3. Randomly selects one email from matching templates
4. Falls back to hardcoded template IDs if no matches found
