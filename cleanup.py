#!/usr/bin/env python3
"""
Project cleanup utility for maintaining a clean codebase.
Removes cache files, temporary files, and other clutter.
"""
import os
import shutil
import sys
from pathlib import Path

def remove_pycache():
    """Remove all __pycache__ directories."""
    print("Removing __pycache__ directories...")
    removed_count = 0
    
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            try:
                shutil.rmtree(pycache_path)
                print(f"  Removed: {pycache_path}")
                removed_count += 1
            except Exception as e:
                print(f"  Error removing {pycache_path}: {e}")
    
    print(f"Removed {removed_count} __pycache__ directories")

def remove_temp_files():
    """Remove temporary files and logs."""
    print("Removing temporary files...")
    temp_patterns = [
        '*.pyc',
        '*.pyo',
        '*.log',
        '*.tmp',
        '.DS_Store',
        'Thumbs.db'
    ]
    
    removed_count = 0
    for pattern in temp_patterns:
        for file_path in Path('.').rglob(pattern):
            try:
                file_path.unlink()
                print(f"  Removed: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"  Error removing {file_path}: {e}")
    
    print(f"Removed {removed_count} temporary files")

def check_unused_imports():
    """Check for potentially unused imports (basic check)."""
    print("Checking for potentially unused imports...")
    
    # This is a basic check - for more thorough analysis, use tools like vulture or unimport
    python_files = list(Path('.').rglob('*.py'))
    
    for file_path in python_files:
        if 'venv' in str(file_path) or '__pycache__' in str(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Basic check for imports that might be unused
            lines = content.split('\n')
            imports = []
            
            for line in lines:
                line = line.strip()
                if line.startswith('import ') or line.startswith('from '):
                    imports.append(line)
            
            if len(imports) > 10:  # Arbitrary threshold
                print(f"  {file_path}: {len(imports)} imports (consider reviewing)")
                
        except Exception as e:
            print(f"  Error reading {file_path}: {e}")

def show_project_stats():
    """Show basic project statistics."""
    print("Project Statistics:")
    print("-" * 40)
    
    # Count files by type
    file_counts = {}
    total_lines = 0
    
    for file_path in Path('.').rglob('*'):
        if file_path.is_file() and 'venv' not in str(file_path):
            suffix = file_path.suffix or 'no_extension'
            file_counts[suffix] = file_counts.get(suffix, 0) + 1
            
            # Count lines in Python files
            if suffix == '.py':
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = len(f.readlines())
                        total_lines += lines
                except:
                    pass
    
    print(f"Python files: {file_counts.get('.py', 0)}")
    print(f"Total Python lines: {total_lines}")
    print(f"Markdown files: {file_counts.get('.md', 0)}")
    print(f"SQL files: {file_counts.get('.sql', 0)}")
    print(f"Other files: {sum(v for k, v in file_counts.items() if k not in ['.py', '.md', '.sql'])}")

def main():
    """Main cleanup function."""
    print("Cold Email Server - Project Cleanup Utility")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        action = sys.argv[1].lower()
        
        if action == 'cache':
            remove_pycache()
        elif action == 'temp':
            remove_temp_files()
        elif action == 'imports':
            check_unused_imports()
        elif action == 'stats':
            show_project_stats()
        elif action == 'all':
            remove_pycache()
            remove_temp_files()
            print()
            show_project_stats()
        else:
            print(f"Unknown action: {action}")
            print("Available actions: cache, temp, imports, stats, all")
            return 1
    else:
        print("Usage: python cleanup.py <action>")
        print("Actions:")
        print("  cache   - Remove __pycache__ directories")
        print("  temp    - Remove temporary files")
        print("  imports - Check for unused imports")
        print("  stats   - Show project statistics")
        print("  all     - Run cache and temp cleanup, show stats")
        return 1
    
    print("\nCleanup completed!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
