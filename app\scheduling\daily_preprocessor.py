#!/usr/bin/env python3
"""
Daily email preprocessing system.
Runs once daily to build priority queues and manage quotas.
"""
import datetime
import heapq
from typing import Dict, List, Tuple, Any
from collections import defaultdict

from app.scheduling.quota_manager import QuotaManager, EmailItem
from app.database.supabase_client import db
from app.config import SCHEDULING_CONFIG
from app.utils.logger import setup_logger

logger = setup_logger('daily_preprocessor')

class DailyPreprocessor:
    """Handles daily preprocessing of emails and quota management."""

    def __init__(self):
        self.quota_manager = QuotaManager()
        self.daily_queues: Dict[str, List[Tuple[float, EmailItem]]] = defaultdict(list)
        self.postponed_emails: List[EmailItem] = []
        self.config = SCHEDULING_CONFIG

    def run_daily_preprocessing(self, target_date: datetime.date = None) -> Dict[str, Any]:
        """
        Run the complete daily preprocessing workflow.

        Args:
            target_date: Date to process emails for (defaults to today)

        Returns:
            Dict with processing results and statistics
        """
        if target_date is None:
            target_date = datetime.date.today()

        logger.info(f"Starting daily preprocessing for {target_date}")

        # Step 1: Initialize account quotas
        accounts = self._initialize_account_quotas()

        # Step 2: Fetch scheduled emails for the day
        scheduled_emails = self._fetch_scheduled_emails(target_date)

        # Step 3: Process emails and build priority queues
        processing_results = self._process_emails(scheduled_emails)

        # Step 4: Handle postponed emails
        self._handle_postponed_emails()

        # Step 5: Generate summary statistics
        stats = self._generate_statistics(accounts, scheduled_emails, processing_results)

        logger.info(f"Daily preprocessing completed. Processed {len(scheduled_emails)} emails")

        return {
            'date': target_date.isoformat(),
            'accounts_processed': len(accounts),
            'emails_scheduled': len(scheduled_emails),
            'emails_postponed': len(self.postponed_emails),
            'daily_queues': {acc_id: len(queue) for acc_id, queue in self.daily_queues.items()},
            'statistics': stats,
            'processing_results': processing_results
        }

    def _initialize_account_quotas(self) -> List[Dict[str, Any]]:
        """Initialize quotas for all active email accounts."""
        try:
            accounts = db.get_email_accounts(active_only=True)

            for account in accounts:
                account_id = account['id']
                daily_limit = account.get('daily_limit', 10)
                self.quota_manager.initialize_account_quota(account_id, daily_limit)

            logger.info(f"Initialized quotas for {len(accounts)} accounts")
            return accounts

        except Exception as e:
            logger.error(f"Error initializing account quotas: {str(e)}")
            return []

    def _fetch_scheduled_emails(self, target_date: datetime.date) -> List[EmailItem]:
        """Fetch all emails scheduled for the target date."""
        try:
            # Calculate date range for the target date
            start_time = datetime.datetime.combine(target_date, datetime.time.min)
            end_time = datetime.datetime.combine(target_date, datetime.time.max)

            # Fetch emails from database
            emails_data = db.get_emails_for_date_range(start_time, end_time)

            # Convert to EmailItem objects
            email_items = []
            for email_data in emails_data:
                email_item = EmailItem(
                    email_id=email_data['id'],
                    account_id=email_data['account_id'],
                    scheduled_time=datetime.datetime.fromisoformat(email_data['scheduled_time'].replace('Z', '+00:00')),
                    is_followup=email_data.get('is_follow_up', False)
                )

                # Adjust scheduled time to business hours if needed
                email_item.scheduled_time = self.quota_manager.adjust_to_business_hours(email_item.scheduled_time)

                email_items.append(email_item)

            logger.info(f"Fetched {len(email_items)} emails for {target_date}")
            return email_items

        except Exception as e:
            logger.error(f"Error fetching scheduled emails: {str(e)}")
            return []

    def _process_emails(self, emails: List[EmailItem]) -> Dict[str, Any]:
        """Process emails and build priority queues per account."""
        results = {
            'followups_scheduled': 0,
            'initials_scheduled': 0,
            'followups_postponed': 0,
            'initials_postponed': 0,
            'quota_exceeded_accounts': []
        }

        # Group emails by account
        emails_by_account = defaultdict(list)
        for email in emails:
            emails_by_account[email.account_id].append(email)

        # Process each account separately
        for account_id, account_emails in emails_by_account.items():
            quota = self.quota_manager.get_quota(account_id)
            if not quota:
                logger.warning(f"No quota found for account {account_id}, skipping")
                continue

            # Separate follow-ups and initial emails
            followups = [e for e in account_emails if e.is_followup]
            initials = [e for e in account_emails if not e.is_followup]

            # Sort by scheduled time (earliest first)
            followups.sort(key=lambda x: x.scheduled_time)
            initials.sort(key=lambda x: x.scheduled_time)

            # Process follow-ups first (they have priority)
            for email in followups:
                if quota.allocate_followup():
                    self._add_to_daily_queue(email)
                    results['followups_scheduled'] += 1
                else:
                    self._postpone_email(email)
                    results['followups_postponed'] += 1

            # Process initial emails (can use leftover follow-up quota)
            for email in initials:
                if quota.allocate_initial():
                    self._add_to_daily_queue(email)
                    results['initials_scheduled'] += 1
                else:
                    self._postpone_email(email)
                    results['initials_postponed'] += 1

            # Check if account exceeded quota
            if results['followups_postponed'] > 0 or results['initials_postponed'] > 0:
                results['quota_exceeded_accounts'].append(account_id)

            logger.info(f"Account {account_id}: "
                       f"F-up quota: {quota.followup_used}/{quota.followup_quota}, "
                       f"Initial quota: {quota.initial_used}/{quota.initial_quota}")

        return results

    def _add_to_daily_queue(self, email: EmailItem):
        """Add email to the daily priority queue for its account."""
        priority_score = self.quota_manager.calculate_priority_score(email)
        email.priority_score = priority_score

        # Use heapq for priority queue (min-heap)
        heapq.heappush(self.daily_queues[email.account_id], (priority_score, email))

    def _postpone_email(self, email: EmailItem):
        """Postpone an email and add to postponed list."""
        postponed_email = self.quota_manager.postpone_email(email)
        self.postponed_emails.append(postponed_email)

    def _handle_postponed_emails(self):
        """Update postponed emails in the database."""
        if not self.postponed_emails:
            return

        try:
            for email in self.postponed_emails:
                # Update the scheduled time in the database
                db.update_email_scheduled_time(email.email_id, email.scheduled_time)

            logger.info(f"Updated {len(self.postponed_emails)} postponed emails in database")

        except Exception as e:
            logger.error(f"Error updating postponed emails: {str(e)}")

    def _generate_statistics(self, accounts: List[Dict], emails: List[EmailItem],
                           results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive statistics for the preprocessing run."""
        stats = {
            'total_accounts': len(accounts),
            'total_emails_processed': len(emails),
            'emails_by_type': {
                'followups': len([e for e in emails if e.is_followup]),
                'initials': len([e for e in emails if not e.is_followup])
            },
            'quota_utilization': {},
            'business_hours_adjustments': 0,
            'processing_results': results
        }

        # Calculate quota utilization per account
        for account_id, quota in self.quota_manager.quotas.items():
            utilization = {
                'daily_limit': quota.daily_limit,
                'followup_quota': quota.followup_quota,
                'initial_quota': quota.initial_quota,
                'followup_used': quota.followup_used,
                'initial_used': quota.initial_used,
                'followup_utilization_pct': (quota.followup_used / quota.followup_quota * 100) if quota.followup_quota > 0 else 0,
                'initial_utilization_pct': (quota.initial_used / quota.initial_quota * 100) if quota.initial_quota > 0 else 0,
                'total_utilization_pct': ((quota.followup_used + quota.initial_used) / quota.daily_limit * 100)
            }
            stats['quota_utilization'][account_id] = utilization

        return stats

    def get_daily_queue_for_account(self, account_id: str) -> List[EmailItem]:
        """Get the sorted daily queue for a specific account."""
        if account_id not in self.daily_queues:
            return []

        # Return emails sorted by priority (already sorted in heap)
        return [email for _, email in sorted(self.daily_queues[account_id])]

    def get_next_email_for_account(self, account_id: str) -> EmailItem:
        """Get the next email to send for a specific account."""
        if account_id not in self.daily_queues or not self.daily_queues[account_id]:
            return None

        # Pop the highest priority email (lowest score)
        _, email = heapq.heappop(self.daily_queues[account_id])
        return email
