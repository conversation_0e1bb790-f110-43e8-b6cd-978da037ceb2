#!/usr/bin/env python3
"""
Main entry point for the Cold Email Server
"""
import argparse
import sys

from app.config import update_config, SERVER_CONFIG
from app.utils.logger import setup_logger
from app.email.sender import test_zoho_auth
from app.server import run_server

# Set up logger
logger = setup_logger()

def parse_arguments():
    """
    Parse command line arguments.

    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description='Cold Email Server with Tracking')
    parser.add_argument('password', nargs='?', help='Zoho Mail password or app password (required for sending emails)')
    parser.add_argument('--email', help='Zoho Mail email address (overrides the one in EMAIL_CONFIG)')
    parser.add_argument('--sender-name', help='Sender name for email signature (overrides the one in EMAIL_CONFIG)')
    parser.add_argument('--host', default=SERVER_CONFIG['host'], help='Host to bind to')
    parser.add_argument('--port', type=int, default=SERVER_CONFIG['port'], help='Port to bind to')
    parser.add_argument('--base-url', default=SERVER_CONFIG['base_url'],
                        help='Public URL for tracking (e.g., ngrok URL)')
    parser.add_argument('--no-tracking', action='store_true', help='Disable email open tracking')
    parser.add_argument('--no-follow-ups', action='store_true', help='Disable automatic follow-up emails')
    parser.add_argument('--no-keep-alive', action='store_true', help='Disable keep-alive mechanism (not recommended for Render)')
    parser.add_argument('--no-email-processor', action='store_true', help='Disable automatic processing of scheduled emails')
    # Follow-ups are now handled directly in the email processor
    parser.add_argument('--test-auth', action='store_true', help='Test Zoho authentication and exit')
    parser.add_argument('--test-mode', action='store_true', help='Run in test mode with quick follow-ups (1 minute intervals)')
    parser.add_argument('--use-scheduler', action='store_true', help='Force use of the production scheduler service (default in production mode)')
    parser.add_argument('--no-scheduler', action='store_true', help='Use legacy queue system instead of production scheduler')
    parser.add_argument('--smtp-server', help='SMTP server (overrides the default smtp.zoho.eu)')
    parser.add_argument('--smtp-port', type=int, help='SMTP port (overrides the default 465)')
    parser.add_argument('--tls', action='store_true', help='Use TLS instead of SSL (changes port to 587 if not specified)')
    parser.add_argument('--warm-up', action='store_true', help='Use warm-up emails instead of production emails')

    return parser.parse_args()

def main():
    """Main entry point."""
    # Parse command line arguments
    args = parse_arguments()

    # Update configuration based on command line arguments
    update_config(args)

    # Test authentication if requested
    if args.test_auth:
        logger.info("Testing Zoho Mail authentication...")
        success, message = test_zoho_auth()
        if success:
            logger.info("✓ Authentication successful!")
            return 0
        else:
            logger.error(f"✗ Authentication failed: {message}")
            return 1

    # Determine if various features should be enabled
    keep_alive = not args.no_keep_alive
    process_emails_enabled = not args.no_email_processor

    # Determine which email system to use
    if args.test_mode:
        # Test mode uses the legacy queue system for quick testing
        use_scheduler = False
        logger.info("Test mode: Using legacy queue system for quick email processing")
    elif args.no_scheduler:
        # Explicitly requested legacy system
        use_scheduler = False
        logger.info("Using legacy queue system (explicitly requested)")
    elif args.use_scheduler:
        # Explicitly requested scheduler
        use_scheduler = True
        logger.info("Using production scheduler service (explicitly requested)")
    else:
        # Default to production scheduler for normal operation
        use_scheduler = True
        logger.info("Production mode: Using production scheduler service with daily preprocessing")

    # If test mode is enabled, make sure email processing is also enabled
    if args.test_mode and args.no_email_processor:
        logger.warning("Test mode requires email processing to be enabled. Enabling email processor.")
        process_emails_enabled = True

    # If scheduler is enabled, make sure email processing is also enabled
    if use_scheduler and args.no_email_processor:
        logger.warning("Scheduler service requires email processing to be enabled. Enabling email processor.")
        process_emails_enabled = True

    # Run the server
    run_server(
        host=args.host,
        port=args.port,
        keep_alive_arg=keep_alive,
        process_emails_enabled_arg=process_emails_enabled,
        use_scheduler_arg=use_scheduler
    )
    return 0

if __name__ == "__main__":
    sys.exit(main())
