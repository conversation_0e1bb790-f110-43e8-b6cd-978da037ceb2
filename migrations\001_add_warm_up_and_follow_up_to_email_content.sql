-- Migration: Add warm-up mode and follow-up tracking to email_content table
-- Date: 2024
-- Description: Adds is_warm_up and follow_up columns to email_content table

-- Add is_warm_up column to email_content table
ALTER TABLE email_content 
ADD COLUMN IF NOT EXISTS is_warm_up BOOLEAN NOT NULL DEFAULT FALSE;

-- Add follow_up column to email_content table  
ALTER TABLE email_content 
ADD COLUMN IF NOT EXISTS follow_up INTEGER NOT NULL DEFAULT 0;

-- Add comments to document the new columns
COMMENT ON COLUMN email_content.is_warm_up IS 'Indicates whether the email is for warm-up (true) or production use (false)';
COMMENT ON COLUMN email_content.follow_up IS 'Indicates which follow-up this email is: 0 = initial message, 1 = 1st follow-up, 2 = 2nd follow-up, etc.';

-- Create index for efficient filtering by warm-up mode and follow-up number
CREATE INDEX IF NOT EXISTS idx_email_content_warm_up_follow_up 
ON email_content(is_warm_up, follow_up);

-- Create index for efficient filtering by warm-up mode only
CREATE INDEX IF NOT EXISTS idx_email_content_warm_up 
ON email_content(is_warm_up);
