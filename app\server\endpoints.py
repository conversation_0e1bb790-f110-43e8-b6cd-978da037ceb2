#!/usr/bin/env python3
"""
API endpoint implementations for the Cold Email Server
"""
import time
import datetime
from typing import Dict, Any

from app.config import SERVER_CONFIG
from app.utils.logger import setup_logger
from app.database.supabase_client import db
from app.server.queue import email_queue, queue_lock

# Set up logger
logger = setup_logger('server.endpoints')

def get_priority_queue_state() -> Dict[str, Any]:
    """
    Get the current state of the in-memory priority queue.

    Returns:
        dict: Dictionary containing the current state of the priority queue
    """
    logger.info("=== Retrieving priority queue state ===")

    result = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "queue_size": email_queue.qsize(),
        "queue_items": []
    }

    try:
        # Get the contents of the priority queue without removing items
        with queue_lock:
            if not email_queue.empty():
                # Copy the queue items to a list for inspection
                queue_items = list(email_queue.queue)

                # Sort by priority (scheduled time)
                queue_items.sort(key=lambda x: x[0])

                # Add each item to the result
                for priority, email in queue_items:
                    scheduled_time = datetime.datetime.fromtimestamp(priority)
                    now = datetime.datetime.now()
                    time_diff = scheduled_time - now

                    email_id = email.get('id')
                    recipient_email = email.get('recipient_email', 'Unknown')

                    subject = email.get('subject')
                    status = email.get('status')
                    is_follow_up = email.get('is_follow_up', False)
                    follow_up_num = email.get('follow_up', 0)

                    queue_item = {
                        "id": email_id,
                        "recipient": recipient_email,
                        "subject": subject,
                        "status": status,
                        "scheduled_time": scheduled_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "time_until_send": f"{time_diff.total_seconds():.0f} seconds",
                        "is_follow_up": is_follow_up,
                        "follow_up": follow_up_num
                    }

                    result["queue_items"].append(queue_item)

        # Add summary information
        if result["queue_size"] > 0:
            result["next_email"] = result["queue_items"][0]
        else:
            result["next_email"] = None

        logger.info(f"Retrieved priority queue state: {result['queue_size']} items in queue")
        return result

    except Exception as e:
        error_msg = f"Error retrieving priority queue state: {str(e)}"
        logger.error(error_msg)
        result["error"] = error_msg
        return result

def get_all_emails_in_queue() -> Dict[str, Any]:
    """
    Retrieve all emails from the email_queue table.

    Returns:
        dict: Dictionary containing all emails in the queue
    """
    logger.info("=== Retrieving all emails from the queue ===")

    result = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "emails": []
    }

    try:
        # Get emails from Supabase
        response = db.client.table('email_queue').select('*').execute()
        emails = db._handle_response(response)
        logger.info(f"Retrieved {len(emails)} emails from database")

        # Process each email to format timestamps
        for email in emails:
            # Create a copy to avoid modifying the original
            email_data = dict(email)

            # Format timestamps for better readability
            for key in ['scheduled_time', 'sent_time', 'created_at', 'updated_at', 'last_attempt_time']:
                if email_data.get(key):
                    try:
                        # Try to parse and format the timestamp
                        dt = datetime.datetime.fromisoformat(email_data[key].replace('Z', '+00:00'))
                        email_data[key] = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except (ValueError, AttributeError):
                        # Keep original if parsing fails
                        pass

            result["emails"].append(email_data)

        logger.info("Successfully retrieved all emails from the queue")
        return result

    except Exception as e:
        error_msg = f"Error retrieving emails from queue: {str(e)}"
        logger.error(error_msg)
        result["error"] = error_msg
        return result

def process_scheduled_emails_test() -> Dict[str, Any]:
    """
    Test endpoint for viewing the email queue and scheduled emails.
    This function shows the current state of the email queue and upcoming emails
    without actually sending any emails.

    Returns:
        dict: Detailed information about the emails in the queue and database
    """
    logger.info("=== TEST: Viewing email queue and scheduled emails ===")

    result = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "queue_size": email_queue.qsize(),
        "queue_contents": [],
        "upcoming_emails": [],
        "follow_ups_to_schedule": [],
        "errors": []
    }

    try:
        # Get the contents of the priority queue without removing items
        with queue_lock:
            if not email_queue.empty():
                # Copy the queue items to a list for inspection
                queue_items = list(email_queue.queue)

                # Sort by priority (scheduled time)
                queue_items.sort(key=lambda x: x[0])

                # Add each item to the result
                for priority, email in queue_items:
                    scheduled_time = datetime.datetime.fromtimestamp(priority)
                    now = datetime.datetime.now()
                    time_diff = scheduled_time - now

                    email_id = email.get('id')
                    recipient_email = email.get('recipient_email')
                    subject = email.get('subject')
                    is_follow_up = email.get('is_follow_up', False)
                    follow_up_num = email.get('follow_up', 0)

                    queue_item = {
                        "id": email_id,
                        "recipient": recipient_email,
                        "subject": subject,
                        "scheduled_time": scheduled_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "time_until_send": f"{time_diff.total_seconds():.0f} seconds",
                        "is_follow_up": is_follow_up,
                        "follow_up": follow_up_num
                    }

                    result["queue_contents"].append(queue_item)

                    # Check if this email would have a follow-up scheduled
                    if follow_up_num < 5:
                        # Determine follow-up schedule based on test mode
                        if SERVER_CONFIG['test_mode']:
                            # In test mode, use 1-minute intervals for quick testing
                            follow_up_schedule = {
                                0: 0,   # 1st follow-up: 1 minute after initial email
                                1: 0,   # 2nd follow-up: 1 minute after 1st follow-up
                                2: 0,   # 3rd follow-up: 1 minute after 2nd follow-up
                                3: 0,   # 4th follow-up: 1 minute after 3rd follow-up
                                4: 0    # 5th follow-up: 1 minute after 4th follow-up
                            }
                            follow_up_days = 0  # No days added in test mode
                            logger.info("Test mode: Using 1-minute follow-up intervals")
                        else:
                            # In normal mode, use the standard follow-up schedule
                            follow_up_schedule = {
                                0: 2,   # 1st follow-up: 2 days after initial email
                                1: 4,   # 2nd follow-up: 4 days after initial email
                                2: 7,   # 3rd follow-up: 7 days after initial email
                                3: 14,  # 4th follow-up: 14 days after initial email
                                4: 30   # 5th follow-up: 30 days after initial email
                            }
                            follow_up_days = follow_up_schedule.get(follow_up_num, 2)  # Default to 2 days if not found

                        # Handle follow-up scheduling differently based on test mode
                        if SERVER_CONFIG['test_mode']:
                            # In test mode, schedule follow-up for 1 minute from now
                            now = datetime.datetime.now()
                            follow_up_time = now + datetime.timedelta(minutes=1)
                            follow_up_date = follow_up_time.date()
                            weekend_adjusted = False
                            logger.info(f"Test mode: Scheduling follow-up for 1 minute from now: {follow_up_time.strftime('%H:%M:%S')}")
                        else:
                            # Calculate the follow-up date
                            follow_up_date = datetime.date.today() + datetime.timedelta(days=follow_up_days)

                            # Check if the follow-up date falls on a weekend (5=Saturday, 6=Sunday)
                            weekday = follow_up_date.weekday()
                            weekend_adjusted = False

                            if weekday >= 5:  # Weekend
                                # Move to next Monday (add days needed to reach Monday)
                                days_to_add = 7 - weekday
                                follow_up_date = follow_up_date + datetime.timedelta(days=days_to_add)
                                weekend_adjusted = True
                                logger.info(f"Follow-up would fall on a weekend, moved to Monday {follow_up_date}")

                            # Generate a random time between 9am-5pm on the follow-up date
                            follow_up_time = db.generate_random_time(follow_up_date)

                        # In a real follow-up, we would include the previous email content
                        # to create a conversation thread
                        thread_info = {
                            "includes_previous_content": True,
                            "subject_format": f"Re: {subject}",
                            "thread_style": "Quoted previous email with indentation"
                        }

                        follow_up_info = {
                            "original_email_id": email_id,
                            "follow_up_days": follow_up_days,
                            "follow_up_number": follow_up_num + 1,
                            "recipient": recipient_email,
                            "scheduled_time": follow_up_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "weekend_adjusted": weekend_adjusted,
                            "thread_info": thread_info
                        }
                        result["follow_ups_to_schedule"].append(follow_up_info)
                    else:
                        # Log that this is the last follow-up and no more will be scheduled
                        logger.info(f"LAST FOLLOW-UP IN QUEUE: Email {email_id} is the final (5th) follow-up to {recipient_email}")
                        logger.info(f"LAST FOLLOW-UP IN QUEUE: No further follow-ups will be scheduled for this email thread")

                        # Add to a special section in the result
                        if "final_follow_ups" not in result:
                            result["final_follow_ups"] = []

                        result["final_follow_ups"].append({
                            "email_id": email_id,
                            "recipient": recipient_email,
                            "subject": subject,
                            "scheduled_time": scheduled_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "follow_up_number": follow_up_num
                        })

            # Get emails from the database that are not yet in the queue
            logger.info("Fetching upcoming emails from the database...")
            upcoming_emails = db.get_emails_to_send(time_window_minutes=120)  # Look ahead 2 hours

            if upcoming_emails:
                # Filter out emails that are already in the queue
                queue_email_ids = [email.get('id') for _, email in queue_items] if 'queue_items' in locals() else []
                upcoming_emails = [email for email in upcoming_emails if email.get('id') not in queue_email_ids]

                # Add upcoming emails to the result
                for email in upcoming_emails:
                    email_id = email.get('id')
                    recipient_email = email.get('recipient_email')
                    subject = email.get('subject')
                    scheduled_time_str = email.get('scheduled_time')
                    is_follow_up = email.get('is_follow_up', False)
                    follow_up_num = email.get('follow_up', 0)

                    try:
                        scheduled_time = datetime.datetime.fromisoformat(scheduled_time_str.replace('Z', '+00:00'))
                        now = datetime.datetime.now()
                        time_diff = scheduled_time - now

                        upcoming_email = {
                            "id": email_id,
                            "recipient": recipient_email,
                            "subject": subject,
                            "scheduled_time": scheduled_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "time_until_send": f"{time_diff.total_seconds():.0f} seconds",
                            "is_follow_up": is_follow_up,
                            "follow_up": follow_up_num
                        }

                        result["upcoming_emails"].append(upcoming_email)
                    except (ValueError, AttributeError) as e:
                        logger.error(f"Error parsing scheduled time for email {email_id}: {str(e)}")

        # Add summary information
        result["queue_summary"] = {
            "total_emails": len(result["queue_contents"]),
            "next_email_time": result["queue_contents"][0]["scheduled_time"] if result["queue_contents"] else "None"
        }

        result["upcoming_summary"] = {
            "total_emails": len(result["upcoming_emails"]),
            "next_fetch_time": (datetime.datetime.now() + datetime.timedelta(minutes=60)).strftime('%Y-%m-%d %H:%M:%S')
        }

        # Return the result
        logger.info("Test completed successfully")
        result["message"] = "Email queue and scheduled emails retrieved successfully"
        return result

    except Exception as e:
        error_msg = f"Error retrieving email queue: {str(e)}"
        logger.error(error_msg)
        result["errors"].append(error_msg)
        return result

def get_scheduler_status() -> Dict[str, Any]:
    """
    Get the current status of the production email scheduler.

    Returns:
        dict: Scheduler status and statistics
    """
    logger.info("=== Getting scheduler status ===")

    try:
        # Import here to avoid circular imports
        from app.scheduling.scheduler_service import scheduler_service
        status = scheduler_service.get_status()
        logger.info("Successfully retrieved scheduler status")
        return status

    except Exception as e:
        error_msg = f"Error getting scheduler status: {str(e)}"
        logger.error(error_msg)
        return {
            "error": error_msg,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

def force_daily_preprocessing() -> Dict[str, Any]:
    """
    Force run daily preprocessing (for testing/manual trigger).

    Returns:
        dict: Preprocessing results
    """
    logger.info("=== Force running daily preprocessing ===")

    try:
        # Import here to avoid circular imports
        from app.scheduling.scheduler_service import scheduler_service
        results = scheduler_service.force_preprocessing()
        logger.info("Successfully completed forced preprocessing")
        return {
            "message": "Daily preprocessing completed successfully",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "results": results
        }

    except Exception as e:
        error_msg = f"Error running daily preprocessing: {str(e)}"
        logger.error(error_msg)
        return {
            "error": error_msg,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }